<div class="reservation-details-overlay close-reservation-modal--js"></div>

<div class="{block datagrid-class}datagrid datagrid-{$control->getFullName()}{/block}" data-refresh-state="{link refreshState!}">
    <div n:snippet="grid">
        {snippetArea gridSnippets}
        {form filter, class => 'ajax', autocomplete => 'off'}
        {block outer-filters}
        {/block}
        {block data}


        <div class="reservation-table-header-wrapper">
            <div class="reservation-page-filter-row pb-1 mb-1 border-bottom">
                <div class="d-flex gap-2 align-items-center">
                    <span>Filter</span>
                    {include 'filtersReservationsTemplate.latte', form => $filter, cssClass => 'filter-reservation-pagination', includeFilters => ['type']}
                </div>
            </div>
            <div class="table-header-reservation mb-2">
                {var $filters = ['date_range', 'name']}
                {if $user->isInRole('admin')}
                    {var $filters[] = 'hide_cancelled'}
                {/if}
                {include 'filtersReservationsTemplate.latte', form => $filter, cssClass => 'filter-reservation-pagination', includeFilters => $filters}
                <div class="d-flex justify-content-between gap-2 align-items-center">
                    <div class="dropdown">
                        <button class="btn p-0 px-50 ps-lg-1 dropdown-toggle hide-arrow d-flex align-items-center gap-50 btn-grey" data-bs-toggle="dropdown">
                            {embeddedSvg 'property-assets/app-assets/images/svg/export.svg',
                                class => 'property-card-icon',
                                fill => '#1D2939',
                                height => 14,
                                width => 14}
                                {_'Export'}
                            {embeddedSvg 'assets/img/svg/arrow-down-bold.svg',
                                class => '',
                                height => 10,
                                width => 10}
                        </button>
                        <div class="dropdown-menu dropdown-menu-with-spacing">
                        <div n:if="$exports">
                                <span n:if="$exports" n:snippet="exports" n:block="exports">
                                    <a class="dropdown-item with-border text-capitalize"
                                    title="{_'Export to excel'}"
                                    n:href="export! id => 1">
                                    {embeddedSvg 'property-assets/app-assets/images/svg/excel.svg',
                                        height => 18,
                                        width => 18}
                                        {_'Export to excel'}
                                    </a>
                                    <a class="dropdown-item with-border text-capitalize"
                                    title="{_'Export to pdf'}"
                                    n:href="export! id => 2">
                                    {embeddedSvg 'property-assets/app-assets/images/svg/pdf.svg',
                                        height => 18,
                                        width => 18}
                                        {_'Export to pdf'}
                                    </a>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="hide-on-tablet">
                        <div class="d-flex justify-content-center justify-content-xxl-start">
                            <ul class="nav nav-tabs mb-0" role="tablist" n:if="$allowReservationButton">
                                <li class="nav-item">
                                    {if isset($product_id)}
                                        <a class="btn btn-dark btn-sm waves-effect btn-36" href="/{$presenter->language|noescape}/property/product/details?id={$product_id}&activeTab=calendar">
                                    {else}
                                        <a class="btn btn-dark btn-sm waves-effect btn-36" href="/{$presenter->language|noescape}/property/reservation/new">
                                    {/if}
                                    <span class="d-flex align-items-center h6 mb-0 gap-25 text-white">
                                        {_'Add new reservation'}
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#ffffff" stroke="#ffffff" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus font-small-4">
                                            <line x1="12" y1="5" x2="12" y2="19"></line>
                                            <line x1="5" y1="12" x2="19" y2="12"></line>
                                        </svg>
                                    </span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="show-on-mobile">
                <div class="mobile-table-title">
                    <span>{_'Upcoming reservations'|capitalize}</span>
                </div>
            </div>
            <div class="table-responsive relative">
                <div class="loader-grid-table d-none">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                </div>
                <table class="table table-hover table-reservation table-sm" n:snippet="table">
                    <thead n:block="header">

                    <tr n:block="header-column-row">
                        <th></th>
                        <th class="text-left col-listing">{_'Listing'|upper}</th>
                        <th class="text-left col-source">{_'Source'|upper}</th>
                        <th class="text-left col-status">{_'Status'|upper}</th>
                        <th class="text-left col-guests">{_'Guests'|upper}</th>
                        <th class="text-left col-check-in">
                            <a n:class="$columns['arrive']->isSortedBy() ? 'sort' : '', 'ajax'" href="{link sort!, sort => $control->getSortNext($columns['arrive'])}" id="datagrid-sort-arrive">
                                {_'Check In'|upper}
                                {if $columns['arrive']->isSortedBy()}
                                    {if $columns['arrive']->isSortAsc()}
                                        <i class="{$iconPrefix}caret-up"></i>
                                    {else}
                                        <i class="{$iconPrefix}caret-down"></i>
                                    {/if}
                                {else}
                                    <i class="{$iconPrefix}sort"></i>
                                {/if}
                            </a>
                        </th>
                        <th class="text-left col-check-out">
                            <a n:class="$columns['departure']->isSortedBy() ? 'sort' : '', 'ajax'" href="{link sort!, sort => $control->getSortNext($columns['departure'])}" id="datagrid-sort-departure">
                                {_'Check Out'|upper}
                                {if $columns['departure']->isSortedBy()}
                                    {if $columns['departure']->isSortAsc()}
                                        <i class="{$iconPrefix}caret-up"></i>
                                    {else}
                                        <i class="{$iconPrefix}caret-down"></i>
                                    {/if}
                                {else}
                                    <i class="{$iconPrefix}sort"></i>
                                {/if}
                            </a>
                        </th>
                        <th n:if="!$user->isInRole('admin')" class="text-left col-arrive-in-days">{_'Booked'|upper}</th>
                        <th n:if="$user->isInRole('admin')" class="text-left col-booked">
                            <a n:class="$columns['booked_date']->isSortedBy() ? 'sort' : '', 'ajax'" href="{link sort!, sort => $control->getSortNext($columns['booked_date'])}" id="datagrid-sort-booked_date">
                                {_'Booked'|upper}
                                {if $columns['booked_date']->isSortedBy()}
                                    {if $columns['booked_date']->isSortAsc()}
                                        <i class="{$iconPrefix}caret-up"></i>
                                    {else}
                                        <i class="{$iconPrefix}caret-down"></i>
                                    {/if}
                                {else}
                                    <i class="{$iconPrefix}sort"></i>
                                {/if}
                            </a>
                        </th>
                        <th class="text-left col-total-payout-brutto">{_'Pay bruto'|upper}</th>
                        <th class="text-left col-total-payout-neto">{_'Pay neto'|upper}</th>
                        <th></th>

                    </tr>
                    </thead>
                    {block tbody}
                        <tbody n:snippet="tbody">
                        {snippetArea items}
                            {foreach $rows as $row}
                                {var $item = $row->getItem()}
                                {var $linkToDetails = $item->is_foreign_reservation ? '/' . $presenter->language . '/property/foreign-reservation/details?id=' . $item->id : '/' . $presenter->language . '/property/reservation/details?id='. $item->id}
                                {var $initialAmount = $item->total_price_without_discount}
                                {var $bruttoAmount = $item->total_price}
                                {var $discountAmount = $initialAmount - $bruttoAmount}
                                {var $agencyProvisionAmount = $bruttoAmount * ($item->agency_provision_percentage / 100) * 1.25}
                                {var $nettoAmount = $bruttoAmount - $agencyProvisionAmount}

                                {if !$user->isInRole('admin') && !in_array($item->source, ['My own', 'Villas guide'])}
                                    {var $itemSource = !$item->is_agency_provision_property ? 'Villas guide' : 'Distribution'}
                                {else}
                                    {var $itemSource = $item->source}
                                {/if}
                                    <tr class="reservation-table-row" data-id="{$row->getId()}" n:snippet="item-{$row->getId()}" n:attr="$row->getControl()->attrs">
                                        <td>
                                            {if $item->is_foreign_reservation && $item->foreign_reservation_approved === 0 && $item->declined == 0}
                                                <div class="reservation-info" data-toggle="tooltip">
                                                    {embeddedSvg 'property-assets/app-assets/images/svg/info.svg',
                                                    class => 'reservation-canceled-icon ',
                                                    fill => '#ffffff',
                                                    height => 14,
                                                    width => 14}
                                                </div>
                                            {else}
                                                {if $item->cancelled || $item->declined}
                                                    <div class="reservation-canceled" data-toggle="tooltip" title="{_'Cancelled reservation'}">
                                                        {embeddedSvg 'property-assets/app-assets/images/svg/close-bold.svg',
                                                            class => 'reservation-canceled-icon ',
                                                            fill => '#ffffff',
                                                            height => 14,
                                                            width => 14}
                                                    </div>
                                                {else}
                                                    <div class="reservation-approved" data-toggle="tooltip" title="{_'Approved reservation'}">
                                                        {embeddedSvg 'property-assets/app-assets/images/svg/checkmark.svg',
                                                            class => 'reservation-approved-icon ',
                                                            fill => '#ffffff',
                                                            height => 14,
                                                            width => 14}
                                                    </div>
                                                {/if}
                                            {/if}
                                        </td>
                                        <td>
                                            <a class="reservation-villa-link reservation-villa-wrapper"data-toggle="tooltip" title="{$item->listing}" target="_blank" href="{if !isset($product_id)}/{$presenter->language|noescape}/property/product/details?id={$item->listing_id}{else}#{/if}">
                                                <img class="reservation-villa-image" src="{$item->picture_path ?? '/property-assets/assets/img/hex-pattern.png'}" alt="{$item->listing|noescape}">
                                                <div>
                                                    <span class="reservation-villa-name">{$item->listing}</span>
                                                    {if !isset($product_id)}
                                                        {embeddedSvg 'assets/img/svg/link-arrow-blue.svg',
                                                        class => 'reservation-link-icon',
                                                        fill => '#007BC8',
                                                        height => 16,
                                                        width => 16}
                                                    {/if}
                                                </div>
                                            </a>
                                        </td>
                                        <td class="reservation-details--js" data-href="{$linkToDetails}">
                                            {var $reservationSource = $sourceMapping[$itemSource] ?? 'unknown'}
                                            <div class="reservation-source-wrapper">
                                                <div class="reservation-dot reservation_source_{$reservationSource}"></div><span class="reservation-source-label">{if $itemSource === 'Distribution'}{_$itemSource}{else}{$itemSource}{/if}</span>
                                            </div>
                                            <span class="reservation-id-wrapper"><span>ID </span>{$item->reservation_code ?? $item->id}</span>
                                        </td>
                                        <td class="reservation-details--js" data-href="{$linkToDetails}">
                                            {if $user->isInRole('admin')}
                                                {if $item->is_foreign_reservation}
                                                    {var $reservationStatus = $statusMapping[$item->status] ?? 'unknown'}
                                                    <div class="reservation-status-foreign reservation_status_{$reservationStatus}">{_$item->status}</div>
                                                    {if $item->approved == 0 && $item->declined == 0}
                                                        <div class="reservation-status-own pending" style="margin-top: 15px;">{_'Pending'}</div>
                                                    {elseif $item->approved}
                                                        <div class="reservation-status-own approved" style="margin-top: 15px;">{_'Approved by VG'}</div>
                                                    {elseif $item->declined}
                                                        <div class="reservation-status-own declined" style="margin-top: 15px;">{_'Declined by VG'}</div>
                                                    {/if}
                                                {else}
                                                    -
                                                {/if}
                                            {else}
                                                {if $item->is_foreign_reservation}
                                                    {if $item->foreign_reservation_approved == 0 && $item->declined == 0}
                                                        <div class="reservation-status-own pending">{_'Prebooked'}</div>
                                                    {elseif $item->approved && $item->cancelled = 0}
                                                        <div class="reservation-status-own approved">{_'Confirmed'}</div>
                                                    {elseif $item->declined || $item->cancelled}
                                                        <div class="reservation-status-own declined">{_'Cancelled'}</div>
                                                    {/if}
                                                {else}
                                                    {if $item->approved && $item->cancelled == 0}
                                                        <div class="reservation-status-own approved">{_'Confirmed'}</div>
                                                    {else}
                                                        <div class="reservation-status-own declined">{_'Cancelled'}</div>
                                                    {/if}
                                                {/if}
                                            {/if}

                                        </td>
                                        <td class="reservation-details--js" data-href="{$linkToDetails}">
                                            {if $item->is_foreign_reservation}
                                                <a class="text-nowrap" target="_blank" href="/{$presenter->language|noescape}/property/foreign-reservation/details?id={$item->id}" style="display: block">
                                                    {if $item->customer}
                                                        <span data-toggle="tooltip" title="{$item->customer}" class="reservation-customer-name">{$item->customer}</span>
                                                    {else}
                                                        -
                                                    {/if}
                                                    {if $item->notes}
                                                        {embeddedSvg 'assets/img/svg/email-2.svg',
                                                            class => '',
                                                            height => 14,
                                                            width => 14}
                                                    {/if}
                                                </a>
                                                <span class="guests-mobile guest-as-secondary text-left text-nowrap">
                                                    {if $item->adults > 0}{_'%d adults', $item->adults}{/if}
                                                    {if $item->children > 0}{if $item->adults > 0}, {/if}{_'%d child', $item->children}{/if}
                                                    {if $item->pets > 0}{if $item->adults > 0 || $item->children > 0}, {/if}{_'%d pets', $item->pets}{/if}
                                                </span>
                                            {else}
                                                <a class="text-nowrap" target="_blank" href="/{$presenter->language|noescape}/property/reservation/details?id={$item->id}" style="display: block">
                                                    {if $item->customer}
                                                        <span data-toggle="tooltip" title="{$item->customer}" class="reservation-customer-name">{$item->customer}</span>
                                                    {else}
                                                        -
                                                    {/if}
                                                    {if $item->notes}
                                                        {embeddedSvg 'assets/img/svg/email-2.svg',
                                                        class => '',
                                                        height => 14,
                                                        width => 14}
                                                    {/if}
                                                </a>
                                                <span class="guests-mobile guest-as-secondary text-left text-nowrap">
                                                    {if $item->adults > 0}{_'%d adults', $item->adults}{/if}
                                                    {if $item->children > 0}{if $item->adults > 0}, {/if}{_'%d child', $item->children}{/if}
                                                    {if $item->pets > 0}{if $item->adults > 0 || $item->children > 0}, {/if}{_'%d pets', $item->pets}{/if}
                                                </span>
                                            {/if}
                                        </td>
                                        <td class="reservation-details--js" data-href="{$linkToDetails}">
                                            {$item->arrive->format('d.m.Y')}
                                        </td>
                                        <td class="reservation-details--js" data-href="{$linkToDetails}">
                                            {$item->departure->format('d.m.Y')}
                                        </td>
                                        <td class="reservation-details--js" data-href="{$linkToDetails}">
                                            {if $item->booked_date !== null}
                                                {$item->booked_date->format('d.m.Y')}
                                            {/if}
                                        </td>
                                        <td class="reservation-details--js" data-href="{$linkToDetails}">{$bruttoAmount|number,2,'.'} €</td>
                                        <td class="reservation-details--js" data-href="{$linkToDetails}">
                                            {if $item->is_foreign_reservation}
                                                -
                                            {else}
                                                {$nettoAmount|number,2,'.'} €
                                            {/if}
                                        </td>
                                        <td class="position-relative cursor-default">
                                            <div class="reservation-col-action">
                                                <button class="text-capitalize details-modal-button reservation-details--js" data-href="{$linkToDetails}" data-id="{$item->id}" title="{_'Details'}">
                                                    <span class="hide-on-mobile">{_'Details'}</span>
                                                    {embeddedSvg 'assets/img/svg/arrow-down-bold.svg',
                                                        class => 'reservation-link-icon show-on-mobile',
                                                        height => 12,
                                                        width => 12}
                                                </button>

                                                
                                                {var $showKebab = ($item->is_foreign_reservation && !$item->approved && $item->status !== 'CANCELLED' && !$item->declined)
                                                || (!$item->is_foreign_reservation && $item->created_by !== null && !$item->cancelled)
                                                || (!$item->is_foreign_reservation && $item->cancelled && $item->deleted === null)
                                                || (!$item->approved && $item->status !== 'CONFIRMADA' && !$item->declined)
                                                || ($item->is_foreign_reservation && !$item->declined)
                                                || ($item->created_by !== null)
                                                
                                                }
                                                <div n:if="$showKebab" class="dropdown  dropdown-button--reservation">
                                                    <button class="btn p-0 px-50 ps-lg-1 dropdown-toggle hide-arrow " data-bs-toggle="dropdown">
                                                        <div class="kebab-menu-circle-wrapper {if !$item->is_listing_published} cancelled-item{/if}">
                                                            <div class="kebab-menu-circle "></div>
                                                            <div class="kebab-menu-circle "></div>
                                                            <div class="kebab-menu-circle "></div>
                                                        </div>
                                                    </button>
                                                    <div class="dropdown-menu">
                                                        {if $user->isInRole('admin') && $item->is_foreign_reservation && !$item->approved && $item->status !== 'CANCELLED' && !$item->declined}
                                                            <a href="/{$presenter->language|noescape}/property/foreign-reservation/approve?id={$item->id}"
                                                            title="{_'Approve'}"
                                                            class="action-icon action-icon-first action-icon--approve dropdown-item text-capitalize"
                                                            data-datagrid-confirm="Are you sure?">
                                                                {_'Approve'}
                                                            </a>
                                                        {/if}
                                                        {if $user->isInRole('admin') && $item->is_foreign_reservation && !$item->declined}
                                                            <a href="/{$presenter->language|noescape}/property/foreign-reservation/decline?id={$item->id}"
                                                            title="{_'Decline'}"
                                                            class="action-icon action-icon-first action-icon--cancel dropdown-item text-capitalize"
                                                            data-datagrid-confirm="Are you sure?">
                                                                {_'Cancel'}
                                                            </a>
                                                        {/if}
                                                        {if !$item->is_foreign_reservation && $item->created_by !== null && !$item->cancelled}
                                                            {if isset($product_id)}
                                                                <a href="/{$presenter->language|noescape}/property/reservation/delete?id={$item->id}&product_id={$item->listing_id}"
                                                                   title="{_'Cancel'}"
                                                                   class="action-icon action-icon-first action-icon--cancel dropdown-item text-capitalize"
                                                                   data-datagrid-confirm="Are you sure you want to cancel this reservation?">
                                                                    {_'Cancel'}
                                                                </a>
                                                            {else}
                                                                <a href="/{$presenter->language|noescape}/property/reservation/delete?id={$item->id}"
                                                                   title="{_'Cancel'}"
                                                                   class="action-icon action-icon-first action-icon--cancel dropdown-item text-capitalize"
                                                                   data-datagrid-confirm="Are you sure you want to cancel this reservation?">
                                                                    {_'Cancel'}
                                                                </a>
                                                            {/if}
                                                        {/if}
                                                        {if !$item->is_foreign_reservation && $item->created_by !== null && $item->deleted === null}
                                                            {if isset($product_id)}
                                                                <a href="/{$presenter->language|noescape}/property/reservation/remove?id={$item->id}&product_id={$item->listing_id}"
                                                                   title="{_'Delete'}"
                                                                   class="action-icon action-icon-first action-icon--delete dropdown-item text-capitalize"
                                                                   data-datagrid-confirm="Are you sure you want to delete this reservation?">
                                                                    {_'Delete'}
                                                                </a>
                                                            {else}
                                                                <a href="/{$presenter->language|noescape}/property/reservation/remove?id={$item->id}"
                                                                   title="{_'Delete'}"
                                                                   class="action-icon action-icon-first action-icon--delete dropdown-item text-capitalize"
                                                                   data-datagrid-confirm="Are you sure you want to delete this reservation?">
                                                                    {_'Delete'}
                                                                </a>
                                                            {/if}
                                                        {/if}
                                                        {if $user->isInRole('admin') && $item->is_foreign_reservation}
                                                            {if !$item->approved && $item->status !== 'CONFIRMADA' && !$item->declined}
                                                                <a href="/{$presenter->language|noescape}/property/foreign-reservation/edit?id={$item->id}"
                                                                title="{_'Edit'}"
                                                                class="action-icon action-icon-first action-icon--edit dropdown-item text-capitalize">
                                                                {_'Edit'}
                                                                </a>
                                                            {/if}
                                                        {elseif $item->created_by !== null}
                                                            <a href="/{$presenter->language|noescape}/property/reservation/edit?id={$item->id}"
                                                            title="{_'Edit'}"
                                                            class="action-icon action-icon-first action-icon--edit dropdown-item text-capitalize">
                                                            {_'Edit'}
                                                            </a>
                                                        {/if}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                            {/foreach}
                        {/snippetArea}
                        </tbody>
                    {/block}
                    {block tfoot}
                        <tfoot n:snippet="pagination">
                        <tr n:block="pagination">
                            <td {if $user->isInRole('admin')} colspan="10" {else} colspan="9" {/if} class="row-grid-bottom">
                                {var $paginator = $control['paginator']->getPaginator()}
                                <div class="pagination-grid pagination-grid--reservation gx-0">
                                    <div class="pagination-grid-first-child">
                                        <small class="text-muted">
                                            {if $control->getPerPage() === 'all'}
                                                {='ublaboo_datagrid.all'|translate}
                                            {else}
                                                {$paginator->getOffset() > 0 ? $paginator->getOffset() + 1 : ($paginator->getItemCount() > 0 ? 1 : 0)} - {sizeof($rows) + $paginator->getOffset()}
                                                {='ublaboo_datagrid.from'|translate} {$paginator->getItemCount()}
                                            {/if}
                                        </small>
                                    </div>
                                    <div class="pagination-grid-second-child text-center">
                                        {control paginator}
                                    </div>
                                    <div class="col-per-page text-right">
                                        {input $filter['perPage'], data-autosubmit-per-page => TRUE, class => 'form-control input-sm form-control-sm'}
                                        {input $filter['perPage_submit'], class => 'datagrid-per-page-submit'}
                                    </div>
                                </div>
                            </td>
                        </tr>
                        </tfoot>
                    {/block}
                </table>
            </div>
        </div>
        <div class="modal fade" id="guestRequestModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel1">Guest request</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body"></div>
                </div>
            </div>
        </div>
        {/block}
        {/form}
        {/snippetArea}
    </div>
</div>


