<?php declare(strict_types = 1);

namespace App\Repositories;

use App\Common\Enums\PartnersEnum;
use App\Common\Enums\UserRoleEnum;
use App\Models\Entities\custom\EVisitorConfirmedReservationDetailsEntity;
use App\Models\Entities\custom\EVisitorReservationDetailsEntity;
use App\Models\Entities\custom\ReservationFullDetailsEntity;
use App\Models\Entities\PartnerEntity;
use App\Models\Entities\ProductOwnerEntity;
use App\Models\Entities\ReservationDetailsEntity;
use App\Models\Entities\ReservationEntity;
use App\Models\Entities\ReservationGapEntity;
use App\Models\Entities\ReservationGapLogEntity;
use Dibi\DateTime;
use Dibi\Fluent;
use Dibi\Literal;
use Nette\Security\User;

/**
 * @method ReservationEntity|null getBy(array $conditions, ?string $orderBy = NULL)
 * @method ReservationEntity|null getById(int $id)
 */
class ReservationsRepository extends BaseRepository
{

	/**
	 * @var string
	 */
	protected $table = ReservationEntity::TABLE_NAME;

	protected $entityType = ReservationEntity::class;

	public function getReservationByHubspotDealId($hubspotDealId, bool $cancelled = FALSE)
	{
		$query = $this->db->select('r.*, p.propertyID, p.partner, pa.hubspot_value as partner_hubspot_value, god.hubspot_deal_id')
			->from($this->table)->as('r')
			->leftJoin('products p')->on('p.id = r.product_id')
			->leftJoin(PartnerEntity::TABLE_NAME)->as('pa')->on('pa.internal_name = p.partner')
			->leftJoin('generated_offers_deals god')->on('god.id = r.deal_id')
			->where('deal_id = %i', $hubspotDealId);

		if (!$cancelled) {
			$query->where('r.cancelled IS NULL');
		}

		return $query->fetch();
	}

	/**
	 * @return ReservationDetailsEntity[]
	 */
	public function getProductActiveReservations(int $productId): array
	{
		$list = $this->db->select('re.id, re.arrive, re.departure, re.created_by as user_id, re.is_hidden')
			->from($this->table)->as('re')
			->where('re.product_id = %i and (re.cancelled = 0 or re.cancelled IS NULL) and (re.test = 0 or re.test IS NULL)', $productId)
			->where('re.arrive > DATE_SUB(NOW(), INTERVAL 3 MONTH)')
			->groupBy('re.id')
			->orderBy('re.arrive ASC')
			->execute()
			->setRowClass(ReservationDetailsEntity::class)
			->fetchAll();

		return $list;
	}

	public function getReservationsFromPrivatePartners(): array
	{
		return $this->db->select('r.*')->from($this->table)->as('r')
			->leftJoin('products')->as('p')->on('p.id = r.product_id')
			->where('p.is_private = 1')
			->fetchAll();
	}


	public function getNumOfReservationsByProductIdIstriaHomes(int $productId): ?int
	{
		$today = new DateTime();

		return $this->db->select('count(*)')
			->from($this->table)->as('res')
			->leftJoin('products')->as('pr')->on('pr.id = res.product_id')
			->where('res.departure >= %dt and res.product_id = %i and pr.partner = %s', $today, $productId, PartnersEnum::ISTRIA_HOMES)
			->fetchSingle();
	}

	public function getListByOwner(User $owner): Fluent
	{
		$query = $this->db->select('r.*,
			(select concat("https://villas-guide.com", replace(path, "1024", "400"), internal_filename) FROM pictures pi where pi.product_id = p.id AND pi.active = 1 LIMIT 1) as picture_path')
			->select('p.name as product_name')
			->select('IF(r.created_by IS NULL, 0, 1)')->as('is_owner_reservation')
			->select('concat(in.number_numeric_part, in.number_fixed_part)')->as('invoice_number')
			->select('pr.request_from_guests')->as('message_from_guests')
			->from('reservations r')
			->join('products p ON p.id = r.product_id')
			->leftJoin('invoices')->as('in')->on('r.id = in.reservation_id')
			->leftJoin('pre_reservations')->as('pr')->on('pr.partner_reservation_code = r.reservation_code and p.id = pr.product_id ')
			->where('p.partner = %s', PartnersEnum::PRIVATE)
			->where('p.is_deleted = 0')
			->groupBy('r.id');

		if (!$owner->isInRole('admin')) {
			$query->where('r.product_id IN (SELECT product_id FROM users_products WHERE user_id = %i)', $owner->getId());
		}

		return $query;
	}

	public function getListOfAllReservations(): Fluent
	{
		$reservationsQuery = $this->getQueryForAllVgReservations();
		$foreignReservationsQuery = $this->getQueryForAllForeignReservations();
		$finalQuery = '(' . $reservationsQuery->__toString() . ') UNION (' . $foreignReservationsQuery->__toString() . ')';

		return $this->db->select('*')
			->from(sprintf('(%s)', $finalQuery), 'tr')
			->where('tr.deleted is null');
	}

	public function getVgReservationsFullDetails(int $id): ?ReservationFullDetailsEntity
	{
		return $this->getQueryForAllVgReservations()
			->where('deleted is null and id = %i', $id)
			->execute()
			->setRowClass(ReservationFullDetailsEntity::class)
			->fetch();
	}

	public function getForeignReservationsFullDetails(int $id): ?ReservationFullDetailsEntity
	{
		return $this->getQueryForAllForeignReservations()
			->where('deleted is null and id = %i', $id)
			->execute()
			->setRowClass(ReservationFullDetailsEntity::class)
			->fetch();
	}

	private function getQueryForAllVgReservations(): Fluent
	{
		return $this->db->select(
			'id,
			  foreign_id,
			  created,
			  created_by,
			  is_owner_reservation,
			  source,
			  status,
			  guest_payment_first_installments_status,
			  IFNULL(guest_payment_first_installments_amount, 0) as guest_payment_first_installments_amount,
			  guest_payment_second_installments_status,
			  IFNULL(guest_payment_second_installments_amount, 0) as guest_payment_second_installments_amount,
			  property_owner_first_installments_status,
			  IFNULL(property_owner_first_installments_amount, 0) as property_owner_first_installments_amount,
			  property_owner_second_installments_status,
			  IFNULL(property_owner_second_installments_amount, 0) as property_owner_second_installments_amount,
			  customer,
			  adults,
			  children,
			  pets,
			  arrive_in_days,
			  arrive,
			  departure,
			  number_of_nights,
			  booked_date,
			  listing,
			  listing_id,
			  is_listing_published,
			  reservation_code,
			  IFNULL(total_price, 0) as total_price,
			  IFNULL(price_without_discount, IFNULL(total_price, 0)) as total_price_without_discount,
			  IF(reservation_agency_provision_percentage IS NOT NULL, reservation_agency_provision_percentage, product_agency_provision_percentage)
			  as agency_provision_percentage,
			  notes,
			  approved,
			  declined,
			  cancelled,
			  hidden,
			  deleted,
			  is_foreign_reservation,
			  is_agency_provision_property,
			  has_insurance_amount,
			  cancellation_policy_rule_id,
			  picture_path,
			  foreign_reservation_approved'
		)
			->from($this->db->select('r.id')
				->select('fr.id as foreign_id')
				->select('r.created')
				->select('r.created_by')
				->select('IF(r.created_by IS NULL, 0, 1)')->as('is_owner_reservation')
				->select('if (fr.id is not null, fr.web, IF(r.created_by is null, \'Villas guide\', \'My own\')) as source')
				->select('null as status')
				->select('r.first_installment_payment_status as guest_payment_first_installments_status')
				->select('r.payment_first_installment_amount as guest_payment_first_installments_amount')
				->select('r.second_installment_payment_status as guest_payment_second_installments_status')
				->select('r.payment_second_installment_amount as guest_payment_second_installments_amount')
				->select('r.partner_first_installment_payment_status as property_owner_first_installments_status')
				->select('r.partner_first_installment_payment_amount as property_owner_first_installments_amount')
				->select('r.partner_second_installment_payment_status as property_owner_second_installments_status')
				->select('r.partner_second_installment_payment_amount as property_owner_second_installments_amount')
				->select('CONCAT(IFNULL(r.contact_firstname, \'\'), \' \', IFNULL(r.contact_lastname, \'\')) as customer')
				->select('r.adults')
				->select('r.children')
				->select('r.pets')
				->select('DATEDIFF(r.arrive, NOW()) as arrive_in_days')
				->select('r.arrive')
				->select('r.departure')
				->select('DATEDIFF(r.departure, r.arrive) as number_of_nights')
				->select('r.booked_date')
				->select('p.name as listing')
				->select('p.id as listing_id')
				->select('p.is_published as is_listing_published')
				->select('r.reservation_code')
				->select('r.amount as total_price')
				->select('r.price_without_discount')
				->select(new Literal('IFNULL(
					(select percentage from reservation_agency_provision rap where reservation_id = r.id limit 1)
					, NULL) as reservation_agency_provision_percentage'))
				->select(new Literal('IFNULL(
					(
					select percentage_provision from product_agency_provision papd
					where `type` = \'direct\' and season = YEAR(r.arrive) and papd.product_id = p.id
					order by papd.price_from limit 1
					)
					, 0) as product_agency_provision_percentage'))
				->select('pr.request_from_guests COLLATE utf8mb4_general_ci AS notes')
				->select('1 as approved')
				->select('0 as declined')
				->select('IFNULL(r.cancelled, 0) = 1 as cancelled')
				->select('r.is_hidden as hidden')
				->select('r.deleted')
				->select('0 as is_foreign_reservation')
				->select(new Literal('(
					select count(*) from product_agency_provision pap
					where pap.product_id = p.id and pap.`type` = \'distribution\' and pap.season = YEAR(r.arrive)) > 0 as is_agency_provision_property'))
				->select(new Literal('(select IFNULL(sum(price) > 0, 0) from reservation_insurance ri where ri.reservation_code = r.reservation_code) as has_insurance_amount'))
				->select(new Literal(('(select po.id from booking_terms bt join policies po on po.id = bt.policy_id where bt.product_id = p.id) as cancellation_policy_rule_id')))
				->select('pp.picture_path')->as('picture_path')
				->select('fr.approved as foreign_reservation_approved')
				->from('reservations', 'r')
				->join('products', 'p')->on('p.id = r.product_id')
				->leftJoin('pre_reservations', 'pr')->on('pr.partner_reservation_code = r.reservation_code AND p.id = pr.product_id')
				->leftJoin(new Literal('(
						SELECT fr1.*
						FROM foreign_reservations fr1
						INNER JOIN (
							SELECT reservation_id, MAX(id) AS max_id
							FROM foreign_reservations
							GROUP BY reservation_id
						) fr2 ON fr1.id = fr2.max_id
					)'))->as('fr')->on('fr.reservation_id = r.id') //this will not be the case any more because now we have only one foreign reservation per reservation
				->leftJoin(new Literal('(
					SELECT
					sp.product_id,
					CONCAT(\'https://villas-guide.com\', REPLACE(pic.path, \'1024\', \'400\'), pic.internal_filename) AS picture_path
					FROM sorted_pictures sp
					JOIN pictures pic ON pic.id = sp.picture_id
					WHERE pic.active = 1
					AND sp.ordinal_number = 1)'))->as('pp')->on('pp.product_id = p.id')
				->where('p.partner = \'private\'')
				->where('p.is_deleted = 0')
				->where('(fr.approved IS NULL OR fr.approved = 1 OR fr.declined = 1)'))->as('r');
	}

	private function getQueryForAllForeignReservations(): Fluent
	{
		return $this->db->select(
			'id,
			  foreign_id,
			  created,
			  created_by,
			  is_owner_reservation,
			  source,
			  status,
			  guest_payment_first_installments_status,
			  guest_payment_first_installments_amount,
			  guest_payment_second_installments_status,
			  guest_payment_second_installments_amount,
			  property_owner_first_installments_status,
			  property_owner_first_installments_amount,
			  property_owner_second_installments_status,
			  property_owner_second_installments_amount,
			  customer,
			  adults,
			  children,
			  pets,
			  arrive_in_days,
			  arrive,
			  departure,
			  number_of_nights,
			  booked_date,
			  listing,
			  listing_id,
			  is_listing_published,
			  reservation_code,
			  total_price,
			  total_price as total_price_without_discount,
			  0 as agency_provision_percentage,
			  notes,
			  approved,
			  declined,
			  cancelled,
			  hidden,
			  deleted,
			  is_foreign_reservation,
			  is_agency_provision_property,
			  has_insurance_amount,
			  cancellation_policy_rule_id,
			  picture_path,
			  foreign_reservation_approved'
		)
			->from($this->db->select('fr.id')
				->select('r.id AS foreign_id')
				->select('GROUP_CONCAT(reservation_id) AS reservation_id')
				->select('fr.created')
				->select('fr.created_by')
				->select('0 AS is_owner_reservation')
				->select('fr.web AS source')
				->select('booking_type AS status')
				->select('NULL AS guest_payment_first_installments_status')
				->select('NULL AS guest_payment_first_installments_amount')
				->select('NULL AS guest_payment_second_installments_status')
				->select('NULL AS guest_payment_second_installments_amount')
				->select('NULL AS property_owner_first_installments_status')
				->select('NULL AS property_owner_first_installments_amount')
				->select('NULL AS property_owner_second_installments_status')
				->select('NULL AS property_owner_second_installments_amount')
				->select('CONCAT(fr.name, " ", fr.surname) AS customer')
				->select('fr.adults_number AS adults')
				->select(new Literal('(select SUM(
					 (frc.child1_age IS NOT NULL AND frc.child1_age > 0) +
					 (frc.child2_age IS NOT NULL AND frc.child2_age > 0) +
					 (frc.child3_age IS NOT NULL AND frc.child3_age > 0) +
					 (frc.child4_age IS NOT NULL AND frc.child4_age > 0) +
					 (frc.child5_age IS NOT NULL AND frc.child5_age > 0) +
					 (frc.child6_age IS NOT NULL AND frc.child6_age > 0)
				) as children from foreign_reservations frc where frc.id = fr.id) as children'))
				->select('0 AS pets')
				->select('DATEDIFF(fr.arrive, NOW()) AS arrive_in_days')
				->select('fr.arrive')
				->select('fr.departure')
				->select('DATEDIFF(fr.departure, fr.arrive) AS number_of_nights')
				->select('fr.booking_date AS booked_date')
				->select('p.name AS listing')
				->select('p.id AS listing_id')
				->select('p.is_published AS is_listing_published')
				->select('booking_code AS reservation_code')
				->select('fr.total_price')
				->select('fr.comments AS notes')
				->select('(SELECT MAX(fr1.approved) FROM foreign_reservations fr1 WHERE fr1.booking_code = fr.booking_code) AS approved')
				->select('(SELECT MAX(fr2.declined) FROM foreign_reservations fr2 WHERE fr2.booking_code = fr.booking_code) AS declined')
				->select('0 AS cancelled')
				->select('0 AS hidden')
				->select('null as deleted')
				->select('1 AS is_foreign_reservation')
				->select(new Literal('(
					select count(*) from product_agency_provision pap
					where pap.product_id = p.id and pap.`type` = \'distribution\' and pap.season = YEAR(fr.arrive)) > 0 as is_agency_provision_property'))
				->select('0 as has_insurance_amount')
				->select(new Literal(('(select po.id from booking_terms bt join policies po on po.id = bt.policy_id where bt.product_id = p.id) as cancellation_policy_rule_id')))
				->select('pp.picture_path')->as('picture_path')
				->select('(SELECT MAX(fr1.approved) FROM foreign_reservations fr1 WHERE fr1.booking_code = fr.booking_code) AS foreign_reservation_approved')
				->from(new Literal('(select *, row_number() over (partition by booking_code order by created desc) as row_num from foreign_reservations) AS fr'))
				->join('products p')->on('p.id = fr.product_id')
				->leftJoin('reservations r')->on('r.id = fr.reservation_id')
				->leftJoin(new Literal('(
					SELECT
					sp.product_id,
					CONCAT(\'https://villas-guide.com\', REPLACE(pic.path, \'1024\', \'400\'), pic.internal_filename) AS picture_path
					FROM sorted_pictures sp
					JOIN pictures pic ON pic.id = sp.picture_id
					WHERE pic.active = 1
					AND sp.ordinal_number = 1)'))->as('pp')->on('pp.product_id = p.id')
				->where('p.partner = \'private\'')
				->where('p.is_deleted = 0')
				->groupBy('booking_code'))->as('fr');
	}

	public function getProductOwner(int $productId): ?ProductOwnerEntity
	{
		return $this->db->select('po.*')
			->from('product_owner')->as('po')
			->join('products')->as('pr')->on('po.id = pr.owner_id')
			->where('pr.id = %i', $productId)
			->execute()
			->setRowClass(ProductOwnerEntity::class)
			->fetch();
	}

	public function getDetails(int $id): ?ReservationDetailsEntity
	{
		return $this->db->query('
			select
			id ,
			deal_id,
			reservation_code,
			IFNULL(amount, 0) as amount,
			IFNULL(price_without_discount, IFNULL(amount, 0)) as price_without_discount,
		    IF(reservation_agency_provision_percentage IS NOT NULL, reservation_agency_provision_percentage, product_agency_provision_percentage)
			as agency_provision_percentage,
			contact_email,
			contact_lastname,
			contact_firstname,
			arrive,
			departure,
			phone,
			adults,
			children,
			pets,
			contact_address_line_1,
			guest_street,
			guest_city,
			contact_address_line_2,
			contact_address_line_3,
			postcode,
			payment_first_installment_amount,
			partner_first_installment_payment_status,
			cancelled,
			deal_stage,
			request_from_guests,
			pre_reservation_id,
			request_from_guests_approved,
			created_by,
			product_name,
			product_id,
			price_eur,
			product_distribution_channel_id,
			payment_second_installment_amount,
			total_price_eur,
			is_hidden,
			distribution_price
			from (
				select
				r.id ,
				r.deal_id,
				r.reservation_code,
				r.amount,
				r.price_without_discount,
				r.contact_email,
				r.contact_lastname,
				r.contact_firstname,
				r.arrive,
				r.departure,
				r.phone,
				r.adults,
				r.children,
				r.pets,
				r.contact_address_line_1,
				r.guest_street,
				r.guest_city,
				r.contact_address_line_2,
				r.contact_address_line_3,
				r.postcode,
				r.payment_first_installment_amount,
				r.partner_first_installment_payment_status,
				r.cancelled,
				ge.deal_stage,
				pr.request_from_guests,
				pr.id AS pre_reservation_id,
				IFNULL(pr.request_from_guests_approved, false) AS request_from_guests_approved,
				r.created_by,
				p.name AS product_name,
				p.id AS product_id,
				r.price_eur,
				r.product_distribution_channel_id,
				r.payment_second_installment_amount,
				r.total_price_eur,
				r.is_hidden,
				r.distribution_price,
				(
				    select percentage 
				    from reservation_agency_provision rap 
				    where reservation_id = r.id limit 1
				)
				as reservation_agency_provision_percentage,
				IFNULL((
					select percentage_provision 
					from product_agency_provision papd
					where `type` = \'direct\' and season = YEAR(r.arrive) and papd.product_id = p.id
					order by papd.price_from limit 1), 0)
				as product_agency_provision_percentage
			FROM reservations r 
			LEFT JOIN generated_offers_deals AS ge ON ge.hubspot_deal_id = r.deal_id
			JOIN products p ON p.id = r.product_id 
			LEFT JOIN pre_reservations pr ON pr.partner_reservation_code = r.reservation_code AND p.id = pr.product_id 
			WHERE r.id = %i) as rs', $id)
			->setRowClass(ReservationDetailsEntity::class)
			->fetch();
	}

	public function changeProductId(int $newId, int $oldId): void
	{
		$this->db->update($this->table, [ReservationEntity::PRODUCT_ID => $newId])
			->where([ReservationEntity::PRODUCT_ID => $oldId])->execute();
	}

	/**
	 * @return ReservationEntity[]
	 */
	public function getActiveReservations(int $productId, \DateTimeInterface $created, int $year): array
	{
		$list = $this->db->select('*')
			->from($this->table)
			->where('product_id = %i and IFNULL(cancelled, 0) = 0 and IFNULL(test, 0) = 0', $productId)
			->where('YEAR(arrive) = %i', $year)
			->where('created <= %dt', $created)
			->execute()
			->setRowClass(ReservationEntity::class)
			->fetchAll();

		return $list;
	}

	/**
	 * @return ReservationEntity[]
	 */
	public function findActive(int $productId): array
	{
		return $this->db->select('*')
			->from($this->table)
			->where('product_id = %i and IFNULL(cancelled, 0) = 0', $productId)
			->where('departure >= NOW()')
			->execute()
			->setRowClass(ReservationEntity::class)
			->fetchAll();
	}

	public function getHubspotCountryId(int $country_id): ?string
	{
		return $this->db->select('hubspot_code')
			->from('countries_translations')
			->where('country_id = %i', $country_id)
			->fetchSingle();
	}

	/**
	 * @return ReservationGapEntity[]
	 */
	public function getProductReservationGaps(int $productId): array
	{
		return $this->db->query('select * from reservation_gaps where product_id = %i ORDER BY date_from', $productId)
			->setRowClass(ReservationGapEntity::class)
			->fetchAll();
	}

	public function getProductReservationGapsLog(int $productId): ?ReservationGapLogEntity
	{
		return $this->db->query('select * from reservation_gaps_log where product_id = %i', $productId)
			->setRowClass(ReservationGapLogEntity::class)
			->fetch();
	}

	/**
	 * @return ReservationGapEntity[]
	 */
	public function getReservationGapsListForAdmin(): array
	{
		return $this->db->query('SELECT * 
				FROM reservation_gaps 
				WHERE admin_email_sent_date IS NULL order by product_id, date_from ASC')
			->setRowClass(ReservationGapEntity::class)
			->fetchAll();
	}

	public function removeOldReservationGaps(): void
	{
		$this->db->delete('reservation_gaps')->where('date_from <= NOW()')->execute();
	}

	public function removeIrrelevantReservationGaps(array $productIds): void
	{
		$this->db->delete('reservation_gaps')->where('product_id NOT IN %in', $productIds)->execute();
	}

	public function removeProductReservationGaps(int $productId): void
	{
		$this->db->delete('reservation_gaps')->where('product_id = %i', $productId)->execute();
	}

	public function removeReservationGap(int $id): void
	{
		$this->db->delete('reservation_gaps')->where('id = %i', $id)->execute();
	}

	public function insertReservationGap(int $productId, \DateTimeInterface $dateFrom, \DateTimeInterface $dateTo, string $reason): void
	{
		$this->db->insert('reservation_gaps', [
			'product_id' => $productId,
			'date_from' => $dateFrom,
			'date_to' => $dateTo,
			'reason' => $reason,
		])->execute();
	}

	/**
	 * @param int[] $ids
	 */
	public function setAdminEmailSentForReservationGaps(array $ids): void
	{
		$this->db->update('reservation_gaps', [
			'admin_email_sent_date%sql' => 'NOW()',
		])->where('id in %in', $ids)->execute();
	}

	public function setOwnerEmailSentForReservationGap(int $id): void
	{
		$this->db->update('reservation_gaps', [
			'owner_email_sent_date%sql' => 'NOW()',
		])->where('id = %i', $id)->execute();
	}

	public function updateProductReservationGapsLog(int $productId): void
	{
		$this->db->update('reservation_gaps_log', [
			'updated%sql' => 'NOW()',
		])->where('product_id = %i', $productId)->execute();
	}

	public function insertProductReservationGapLog(int $productId): void
	{
		$this->db->insert('reservation_gaps_log', [
			'product_id' => $productId,
		])->execute();
	}

	public function hasProductReservationChangedAfterDate(int $productId, \DateTimeInterface $date): bool
	{
		return (bool) $this->db->query('select count(*) from reservations r
				left join reservation_log rl on rl.id = rl.reservation_id
				where r.product_id = %i and IFNULL(r.cancelled, 0) = 0 and IFNULL(r.test, 0) = 0
				and r.is_hidden = 0 and (r.created > %d or rl.created > %d)', $productId, $date, $date)
			->fetchSingle();
	}

	public function hasProductSeasonChangedAfterDate(int $productId, \DateTimeInterface $date): bool
	{
		return (bool) $this->db->query('select count(*) from product_seasons ps where ps.product_id = %i and ps.date_from >= NOW()
				and (ps.updated >= %d or ps.created >= %d)', $productId, $date, $date)
			->fetchSingle();
	}

	/**
	 * @return ReservationEntity[]
	 */
	public function getProductActiveReservationsToDate(int $productId, \DateTimeInterface $date): array
	{
		return $this->db->query('select * from reservations r 
				where product_id = %i and IFNULL(cancelled, 0) = 0 and IFNULL(test, 0) = 0 and is_hidden = 0
				and date(%d) < date(departure)
				order by arrive', $productId, $date)
			->setRowClass(ReservationEntity::class)
			->fetchAll();
	}

	public function isAvailablePeriodForReservation(int $productId, \DateTimeInterface $dateStart, \DateTimeInterface $dateEnd): bool
	{
		$overlappingReservationCount = $this->db->query('select count(*) from reservations r 
				where product_id = %i and IFNULL(cancelled, 0) = 0 and IFNULL(test, 0) = 0 and is_hidden = 0
				and date(%d) < departure and date(%d) > arrive', $productId, $dateStart, $dateEnd)
			->fetchSingle();

		return $overlappingReservationCount === 0;
	}

	/**
	 * @return array<string, string>
	 */
	public function getRecruiterDeals(): array
	{
		return $this->db->query('select r.deal_id,  CONCAT(u.firstname, " ", u.lastname) from reservations r 
								inner join products p on p.id = r.product_id 
								inner join users_products up on up.product_id = p.id 
								inner join users u on u.id = up.user_id 
								inner join user_roles ur on ur.user_id = u.id 
								where r.deal_id is not null and ur.role_id = %i
								group by r.id 
								order by r.id desc', UserRoleEnum::RECRUITER)
			->fetchPairs();
	}

	/**
	 * @return EVisitorReservationDetailsEntity[]
	 */
	public function getEvisitorReservations(): array
	{
		return $this->db->query('select r.id as reservation_id, r.contact_email, er.id as evisitor_reservations_id, er.evisitor_data, er.confirmed, er.email_sent_date, r.website_lang
				from reservations r
				join users_products up on up.product_id = r.product_id
				join evisitor_api_users eau on up.user_id = eau.user_id
				join evisitor_api_users_products eaup on eaup.product_id = r.product_id
				left join evisitor_reservations er on er.reservation_id = r.id
				where IFNULL(r.cancelled, 0) = 0 and IFNULL(r.test, 0) = 1 and r.created_by is null and r.contact_email is not null
				and (er.id is null or er.confirmed is null) and NOW() >= DATE_SUB(r.arrive, INTERVAL 7 DAY) and NOW() < DATE_SUB(r.arrive, INTERVAL 1 DAY)
				group by r.id')
			->setRowClass(EVisitorReservationDetailsEntity::class)
			->fetchAll();
	}

	/**
	 * @return EVisitorConfirmedReservationDetailsEntity[]
	 */
	public function getEvisitorConfirmedReservations(): array
	{
		return $this->db->query('select id, evisitor_code, arrive, departure, evisitor_data from (
				select
				er.id,
				eaup.evisitor_code,
				r.arrive,
				r.departure,
				er.evisitor_data,
				(select count(*) from evisitor_reservations_user_data erud where erud.evisitor_reservations_id = er.id) as total_processed_users
				from evisitor_reservations er
				join reservations r on r.id = er.reservation_id
				join users_products up on up.product_id = r.product_id
				join evisitor_api_users eau on up.user_id = eau.user_id
				join evisitor_api_users_products eaup on eaup.product_id = r.product_id
				where er.evisitor_data is not null and er.confirmed is not null and er.is_processed = 0
				and r.arrive is not null and r.departure is not null
				group by er.id order by er.confirmed asc) as evr where total_processed_users = 0')
			->setRowClass(EVisitorConfirmedReservationDetailsEntity::class)
			->fetchAll();
	}

}
